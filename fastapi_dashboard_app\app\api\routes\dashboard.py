"""
Dashboard routes for rendering pages
"""

from fastapi import <PERSON><PERSON><PERSON><PERSON>, Request, Depends, HTTPException
from fastapi.responses import HTMLResponse, RedirectResponse
from fastapi.templating import Jin<PERSON>2Templates

from app.core.security import get_current_user
from app.services.analytics_service import AnalyticsService

router = APIRouter()
templates = Jinja2Templates(directory="templates")

@router.get("/", response_class=HTMLResponse)
async def dashboard_home(request: Request):
    """Main dashboard page - JurnyOn Dashboard Summary"""
    try:
        # Get user from request
        user = get_user_from_request(request)

        context = {
            "request": request,
            "user": user,
            "page_title": "JurnyOn Dashboard"
        }

        return templates.TemplateResponse("dashboard/index.html", context)

    except Exception as e:
        return RedirectResponse(url="/auth/login", status_code=302)

def get_user_from_request(request: Request):
    """Helper function to get user from request"""
    authorization = request.headers.get("Authorization")
    if not authorization or not authorization.startswith("Bearer "):
        # Return demo user for now - in production, you'd redirect to login
        return {
            "username": "<EMAIL>",
            "user_role": "Demo User",
            "token": None
        }

    return {
        "username": "<EMAIL>",
        "user_role": "Admin",
        "token": authorization.split(" ")[1]
    }

@router.get("/analytics", response_class=HTMLResponse)
async def analytics_page(request: Request):
    """Analytics dashboard page"""
    user = get_user_from_request(request)

    context = {
        "request": request,
        "user": user,
        "page_title": "Analytics Dashboard"
    }

    return templates.TemplateResponse("dashboard/analytics.html", context)

@router.get("/users", response_class=HTMLResponse)
async def users_page(request: Request):
    """Users management page"""
    user = get_user_from_request(request)

    context = {
        "request": request,
        "user": user,
        "page_title": "User Management"
    }

    return templates.TemplateResponse("dashboard/users.html", context)

@router.get("/tickets", response_class=HTMLResponse)
async def tickets_page(request: Request):
    """Tickets management page"""
    user = get_user_from_request(request)

    context = {
        "request": request,
        "user": user,
        "page_title": "Ticket Management"
    }

    return templates.TemplateResponse("dashboard/tickets.html", context)

@router.get("/maps", response_class=HTMLResponse)
async def maps_page(request: Request):
    """Maps and location analytics page"""
    user = get_user_from_request(request)

    context = {
        "request": request,
        "user": user,
        "page_title": "Location Analytics"
    }

    return templates.TemplateResponse("dashboard/maps.html", context)

@router.get("/notifications", response_class=HTMLResponse)
async def notifications_page(request: Request):
    """Notifications management page"""
    user = get_user_from_request(request)

    context = {
        "request": request,
        "user": user,
        "page_title": "Notifications"
    }

    return templates.TemplateResponse("dashboard/notifications.html", context)

@router.get("/reports", response_class=HTMLResponse)
async def reports_page(request: Request):
    """Reports and exports page"""
    user = get_user_from_request(request)

    context = {
        "request": request,
        "user": user,
        "page_title": "Reports & Exports"
    }

    return templates.TemplateResponse("dashboard/reports.html", context)
