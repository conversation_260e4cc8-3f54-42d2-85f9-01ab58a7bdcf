"""
Analytics API routes
"""

from fastapi import APIRouter, Depends, HTTPException, Query, Request
from typing import Optional, List
from datetime import datetime

from app.models.schemas import (
    AnalyticsData, DateRangeRequest, SpecificDateRequest,
    EmailRequest, APIResponse, PaginatedResponse
)
from app.core.security import get_current_user
from app.services.analytics_service import AnalyticsService, AuthenticationExpiredException

def get_user_from_request(request: Request):
    """Helper function to get user from request"""
    authorization = request.headers.get("Authorization")
    if not authorization or not authorization.startswith("Bearer "):
        raise HTTPException(status_code=401, detail="Authentication required")

    # Extract the token (which is now the original API token, not a JWT)
    api_token = authorization.split(" ")[1]

    return {
        "username": "<EMAIL>",
        "user_role": "Admin",
        "token": api_token  # This is the original API token from login
    }

router = APIRouter()

@router.get("/today-signups")
async def get_today_signups(request: Request):
    """Get today's signups"""
    try:
        current_user = get_user_from_request(request)
        analytics_service = AnalyticsService(current_user.get("token"))
        count, data = await analytics_service.get_today_signups()

        return APIResponse(
            status="success",
            data={
                "count": count,
                "signups": data
            }
        )
    except AuthenticationExpiredException as e:
        raise HTTPException(status_code=401, detail="Authentication expired")
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/today-tickets")
async def get_today_tickets(request: Request):
    """Get today's ticket purchases"""
    try:
        current_user = get_user_from_request(request)
        analytics_service = AnalyticsService(current_user.get("token"))
        count, data = await analytics_service.get_today_tickets()

        return APIResponse(
            status="success",
            data={
                "count": count,
                "tickets": data
            }
        )
    except AuthenticationExpiredException as e:
        raise HTTPException(status_code=401, detail="Authentication expired")
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/all-users")
async def get_all_users(request: Request):
    """Get all users"""
    try:
        current_user = get_user_from_request(request)
        analytics_service = AnalyticsService(current_user.get("token"))
        count, data = await analytics_service.get_all_users()

        return APIResponse(
            status="success",
            data={
                "total": count,
                "users": data
            }
        )
    except AuthenticationExpiredException as e:
        raise HTTPException(status_code=401, detail="Authentication expired")
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/all-tickets")
async def get_all_tickets(request: Request):
    """Get all tickets"""
    try:
        current_user = get_user_from_request(request)
        analytics_service = AnalyticsService(current_user.get("token"))
        count, data = await analytics_service.get_all_tickets()

        return APIResponse(
            status="success",
            data={
                "total": count,
                "tickets": data
            }
        )
    except AuthenticationExpiredException as e:
        raise HTTPException(status_code=401, detail="Authentication expired")
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/signups-by-date-range")
async def get_signups_by_date_range(
    date_range: DateRangeRequest,
    current_user: dict = Depends(get_current_user)
):
    """Get signups within date range"""
    try:
        analytics_service = AnalyticsService(current_user.get("token"))
        count, data = await analytics_service.get_signups_between_dates(
            date_range.start_date, 
            date_range.end_date
        )
        
        return APIResponse(
            status="success",
            data={
                "count": count,
                "signups": data
            }
        )
    except AuthenticationExpiredException as e:
        raise HTTPException(status_code=401, detail="Authentication expired")
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/tickets-by-date-range")
async def get_tickets_by_date_range(
    date_range: DateRangeRequest,
    current_user: dict = Depends(get_current_user)
):
    """Get tickets within date range"""
    try:
        analytics_service = AnalyticsService(current_user.get("token"))
        count, data = await analytics_service.get_tickets_between_dates(
            date_range.start_date,
            date_range.end_date
        )
        
        return APIResponse(
            status="success",
            data={
                "count": count,
                "tickets": data
            }
        )
    except AuthenticationExpiredException as e:
        raise HTTPException(status_code=401, detail="Authentication expired")
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/user-tickets")
async def get_user_tickets(
    email_request: EmailRequest,
    current_user: dict = Depends(get_current_user)
):
    """Get all tickets for a specific user"""
    try:
        analytics_service = AnalyticsService(current_user.get("token"))
        count, data, loyalty_points, subscription_data = await analytics_service.get_user_tickets(
            email_request.email
        )
        
        return APIResponse(
            status="success",
            data={
                "count": count,
                "tickets": data,
                "loyalty_points": loyalty_points,
                "subscription": subscription_data
            }
        )
    except AuthenticationExpiredException as e:
        raise HTTPException(status_code=401, detail="Authentication expired")
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/referral-info")
async def get_referral_info(current_user: dict = Depends(get_current_user)):
    """Get referral information"""
    try:
        analytics_service = AnalyticsService(current_user.get("token"))
        data = await analytics_service.get_all_referral_info()
        
        return APIResponse(
            status="success",
            data={"referrals": data}
        )
    except AuthenticationExpiredException as e:
        raise HTTPException(status_code=401, detail="Authentication expired")
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/dashboard-summary")
async def get_dashboard_summary(request: Request):
    """Get dashboard summary data"""
    try:
        current_user = get_user_from_request(request)
        analytics_service = AnalyticsService(current_user.get("token"))

        # Get all summary data
        today_signups_count, _ = await analytics_service.get_today_signups()
        today_tickets_count, _ = await analytics_service.get_today_tickets()
        total_users_count, _ = await analytics_service.get_all_users()
        total_tickets_count, _ = await analytics_service.get_all_tickets()

        return APIResponse(
            status="success",
            data={
                "today_signups": today_signups_count or 0,
                "today_tickets": today_tickets_count or 0,
                "total_users": total_users_count or 0,
                "total_tickets": total_tickets_count or 0
            }
        )
    except AuthenticationExpiredException as e:
        raise HTTPException(status_code=401, detail="Authentication expired")
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
