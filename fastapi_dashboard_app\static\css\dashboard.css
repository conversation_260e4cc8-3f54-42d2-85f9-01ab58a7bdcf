:root {
    --primary-color: #ea3f63;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --info-color: #17a2b8;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --sidebar-width: 250px;
}


.progress-bar.bg-primary {
    background-color: var(--primary-color) !important;
}


.card-header.primary-border {
    border-left: 4px solid var(--primary-color);
}








body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;    
    padding: 20px;
}

/* Navigation */
.navbar {
    box-shadow: 0 2px 4px rgba(0,0,0,.1);
    z-index: 1030;
    background-color: var(--primary-color)!important;
}

.navbar-brand {
    font-weight: 600;
    font-size: 1.25rem;
}

.nav-link {
    font-weight: 500;
    transition: all 0.3s ease;
}

.nav-link:hover {
    transform: translateY(-1px);
}

/* Main Content */
.main-content {
    margin-top: 76px; /* Account for fixed navbar */
    padding: 2rem 0;
    min-height: calc(100vh - 76px);
}

/* Cards */
.card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    transition: all 0.3s ease;    
}

/* Home page Cards */
.card-home {
    border: none;
    border-radius: 10px;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    transition: all 0.3s ease;    
    width: 70%;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.25rem 2rem 0 rgba(58, 59, 69, 0.2);
}

.card-header {
    background-color: #f8f9fc;
    border-bottom: 1px solid #e3e6f0;
    border-radius: 10px 10px 0 0 !important;
}

.card-title {
    font-size: 0.8rem;
    font-weight: 600;
    color: var(--primary-color)!important;
}

.card-value {
    font-size: 1.2rem;
    font-weight: 800;
    color: #5a5c69;
}


/* Colored borders for stat cards */
.border-left-primary {
    border-left: 0.25rem solid var(--primary-color) !important;
}

.border-left-success {
    border-left: 0.25rem solid var(--success-color) !important;
}

.border-left-info {
    border-left: 0.25rem solid var(--info-color) !important;
}

.border-left-warning {
    border-left: 0.25rem solid var(--warning-color) !important;
}

/* Text utilities */
.text-xs {
    font-size: 0.7rem;
}

.font-weight-bold {
    font-weight: 700 !important;
}

.text-gray-800 {
    color: #5a5c69 !important;
}

.text-gray-400 {
    color: #858796 !important;
}

/* Avatar */
.avatar-sm {
    width: 2.5rem;
    height: 2.5rem;
}

/* Buttons */
.btn {
    border-radius: 0.35rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
}


/* Login page specific styles */
.login-main {
    min-height: 100vh;
    display: flex;
    align-items: center;
    padding: 0;
    margin: 0;
}

.login-main .login-container {
    width: 100%;
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

/* Alerts */
.alert {
    border-radius: 0.5rem;
    border: none;
}

.alert-success {
    background-color: #d4edda;
    color: #155724;
}

.alert-danger {
    background-color: #f8d7da;
    color: #721c24;
}

.alert-warning {
    background-color: #fff3cd;
    color: #856404;
}

.alert-info {
    background-color: #d1ecf1;
    color: #0c5460;
}

/* Tables */
.table {
    border-radius: 0.5rem;
    overflow: hidden;
}

.table thead th {
    background-color: #f8f9fc;
    border-bottom: 1px solid #e3e6f0;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.8rem;
    color: #5a5c69;
}

/* Badges */
.badge {
    font-weight: 500;
    border-radius: 0.35rem;
}

/* Spinners */
.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .main-content {
        padding: 1rem 0;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .h5 {
        font-size: 1.1rem;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    body {
        background-color: #1a1a1a;
        color: #ffffff;
    }
    
    .card {
        background-color: #2d2d2d;
        color: #ffffff;
    }
    
    .card-header {
        background-color: #3d3d3d;
        border-bottom-color: #4d4d4d;
    }
    
    .navbar {
        background-color: #2d2d2d !important;
    }
    
    .table {
        color: #ffffff;
    }
    
    .table thead th {
        background-color: #3d3d3d;
        color: #ffffff;
    }
}

/* Animation utilities */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}

/* Loading states */
.loading {
    position: relative;
    overflow: hidden;
}

.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* Metric Cards */
.metric-card {
    background: #f8f9fc;
    border: 1px solid #e3e6f0;
    border-radius: 0.35rem;
    padding: 1rem;
    margin-bottom: 0.5rem;
    text-align: center;
}

.metric-label {
    font-size: 0.75rem;
    font-weight: 700;
    text-transform: uppercase;
    color: #5a5c69;
    margin-bottom: 0.5rem;
}

.metric-value {
    font-size: 1.25rem;
    font-weight: 700;
    color: #3a3b45;
}
