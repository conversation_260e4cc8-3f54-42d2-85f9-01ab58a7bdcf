import _plotly_utils.basevalidators


class IncludesrcValidator(_plotly_utils.basevalidators.SrcValidator):
    def __init__(
        self,
        plotly_name="includesrc",
        parent_name="layout.scene.xaxis.autorangeoptions",
        **kwargs,
    ):
        super(IncludesrcValidator, self).__init__(
            plotly_name=plotly_name,
            parent_name=parent_name,
            edit_type=kwargs.pop("edit_type", "none"),
            **kwargs,
        )
