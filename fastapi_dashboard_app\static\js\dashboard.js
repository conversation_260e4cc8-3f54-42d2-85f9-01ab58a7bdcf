// Dashboard JavaScript utilities

// Global variables
let authToken = localStorage.getItem('access_token');

// Axios configuration
if (authToken) {
    axios.defaults.headers.common['Authorization'] = `Bearer ${authToken}`;
}

// API call wrapper
async function apiCall(endpoint, method = 'GET', data = null) {
    try {
        const config = {
            method: method,
            url: endpoint,
            headers: {
                'Authorization': `Bearer ${authToken}`,
                'Content-Type': 'application/json'
            }
        };
        
        if (data && (method === 'POST' || method === 'PUT' || method === 'PATCH')) {
            config.data = data;
        }
        
        const response = await axios(config);
        return response.data;
    } catch (error) {
        if (error.response && error.response.status === 401) {
            // Token expired, redirect to login
            logout();
            return;
        }
        throw error;
    }
}

// Authentication functions
function logout() {
    localStorage.removeItem('access_token');
    localStorage.removeItem('user_role');
    localStorage.removeItem('username');
    window.location.href = '/auth/login';
}

function checkAuth() {
    if (!authToken) {
        window.location.href = '/auth/login';
        return false;
    }
    return true;
}

// Utility functions
function showAlert(message, type = 'info', container = 'body') {
    const alertId = 'alert-' + Date.now();
    const alertHtml = `
        <div id="${alertId}" class="alert alert-${type} alert-dismissible fade show" role="alert">
            <i class="bi bi-${getAlertIcon(type)}"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    if (container === 'body') {
        // Create a fixed alert container if it doesn't exist
        let alertContainer = document.getElementById('global-alert-container');
        if (!alertContainer) {
            alertContainer = document.createElement('div');
            alertContainer.id = 'global-alert-container';
            alertContainer.style.cssText = `
                position: fixed;
                top: 80px;
                right: 20px;
                z-index: 1050;
                max-width: 400px;
            `;
            document.body.appendChild(alertContainer);
        }
        alertContainer.insertAdjacentHTML('beforeend', alertHtml);
    } else {
        document.querySelector(container).insertAdjacentHTML('beforeend', alertHtml);
    }
    
    // Auto-dismiss after 5 seconds
    setTimeout(() => {
        const alert = document.getElementById(alertId);
        if (alert) {
            const bsAlert = new bootstrap.Alert(alert);
            bsAlert.close();
        }
    }, 5000);
}

function getAlertIcon(type) {
    const icons = {
        'success': 'check-circle-fill',
        'danger': 'exclamation-triangle-fill',
        'warning': 'exclamation-triangle-fill',
        'info': 'info-circle-fill'
    };
    return icons[type] || 'info-circle-fill';
}

function formatDate(dateString) {
    if (!dateString) return 'N/A';
    try {
        const date = new Date(dateString);
        return date.toLocaleDateString('en-GB', {
            day: '2-digit',
            month: 'short',
            year: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    } catch (error) {
        return dateString;
    }
}

function formatCurrency(amount) {
    if (amount === null || amount === undefined) return '£0.00';
    return new Intl.NumberFormat('en-GB', {
        style: 'currency',
        currency: 'GBP'
    }).format(amount);
}

function formatNumber(number) {
    if (number === null || number === undefined) return '0';
    return new Intl.NumberFormat('en-GB').format(number);
}

// Loading states
function showLoading(elementId) {
    const element = document.getElementById(elementId);
    if (element) {
        element.innerHTML = `
            <div class="text-center">
                <div class="spinner-border" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
            </div>
        `;
    }
}

function hideLoading(elementId, content = '') {
    const element = document.getElementById(elementId);
    if (element) {
        element.innerHTML = content;
    }
}

// Table utilities
function createDataTable(data, columns, containerId) {
    if (!data || data.length === 0) {
        document.getElementById(containerId).innerHTML = '<p class="text-muted">No data available</p>';
        return;
    }
    
    let tableHtml = `
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead>
                    <tr>
                        ${columns.map(col => `<th>${col.title}</th>`).join('')}
                    </tr>
                </thead>
                <tbody>
    `;
    
    data.forEach(row => {
        tableHtml += '<tr>';
        columns.forEach(col => {
            let value = row[col.field] || '';
            if (col.formatter) {
                value = col.formatter(value, row);
            }
            tableHtml += `<td>${value}</td>`;
        });
        tableHtml += '</tr>';
    });
    
    tableHtml += `
                </tbody>
            </table>
        </div>
    `;
    
    document.getElementById(containerId).innerHTML = tableHtml;
}

// Chart utilities
function createLineChart(elementId, data, title = '') {
    const layout = {
        title: title,
        xaxis: { title: 'Date' },
        yaxis: { title: 'Count' },
        margin: { t: 40, r: 40, b: 40, l: 40 },
        responsive: true
    };
    
    Plotly.newPlot(elementId, data, layout, {
        responsive: true,
        displayModeBar: false
    });
}

function createPieChart(elementId, labels, values, title = '') {
    const data = [{
        values: values,
        labels: labels,
        type: 'pie',
        marker: {
            colors: ['#007bff', '#28a745', '#ffc107', '#dc3545', '#17a2b8']
        }
    }];
    
    const layout = {
        title: title,
        margin: { t: 40, r: 40, b: 40, l: 40 }
    };
    
    Plotly.newPlot(elementId, data, layout, {
        responsive: true,
        displayModeBar: false
    });
}

function createBarChart(elementId, x, y, title = '') {
    const data = [{
        x: x,
        y: y,
        type: 'bar',
        marker: {
            color: '#007bff'
        }
    }];
    
    const layout = {
        title: title,
        xaxis: { title: 'Category' },
        yaxis: { title: 'Count' },
        margin: { t: 40, r: 40, b: 40, l: 40 }
    };
    
    Plotly.newPlot(elementId, data, layout, {
        responsive: true,
        displayModeBar: false
    });
}

// Date utilities
function getDateRange(days) {
    const end = new Date();
    const start = new Date();
    start.setDate(start.getDate() - days);
    
    return {
        start: start.toISOString().split('T')[0],
        end: end.toISOString().split('T')[0]
    };
}

function formatDateForAPI(date) {
    if (typeof date === 'string') {
        return date;
    }
    return date.toISOString().split('T')[0];
}

// Export utilities
function exportToCSV(data, filename) {
    if (!data || data.length === 0) {
        showAlert('No data to export', 'warning');
        return;
    }
    
    const headers = Object.keys(data[0]);
    const csvContent = [
        headers.join(','),
        ...data.map(row => headers.map(header => {
            const value = row[header] || '';
            return `"${value.toString().replace(/"/g, '""')}"`;
        }).join(','))
    ].join('\n');
    
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    a.click();
    window.URL.revokeObjectURL(url);
}

// Initialize dashboard
document.addEventListener('DOMContentLoaded', function() {
    // Check authentication
    if (window.location.pathname !== '/auth/login' && !checkAuth()) {
        return;
    }
    
    // Set active navigation
    setActiveNavigation();
    
    // Initialize tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});

function setActiveNavigation() {
    const currentPath = window.location.pathname;
    const navLinks = document.querySelectorAll('.nav-link');

    navLinks.forEach(link => {
        if (link.getAttribute('href') === currentPath) {
            link.classList.add('active');
        } else {
            link.classList.remove('active');
        }
    });
}

// Dashboard specific functions
async function loadDashboardData() {
    try {
        // Load dashboard summary data
        const summaryData = await apiCall('/api/analytics/dashboard-summary');

        if (summaryData && summaryData.status === 'success') {
            updateDashboardStats(summaryData.data);
        }

        // Load today's signups
        await loadTodaySignups();

        // Set current date and month
        setCurrentDateAndMonth();

    } catch (error) {
        console.error('Error loading dashboard data:', error);
        showErrorMessage('Failed to load dashboard data');
    }
}

function updateDashboardStats(data) {
    // Update total stats
    updateElement('totalUserCount', data.total_users || 0);
    updateElement('zeroTicketBuyerCount', data.zero_ticket_buyers || 0);
    updateElement('ticketBuyerCount', data.ticket_buyers || 0);
    updateElement('deletedUserCount', data.deleted_users || 0);
    updateElement('totalJourneyCount', data.total_journeys || 0);

    // Update current month stats
    updateElement('month-tickets', data.current_month_tickets || 0);
    updateElement('month-signups', data.current_month_signups || 0);
    updateElement('month-new-buyers', data.current_month_new_buyers || 0);
    updateElement('month-sales', `£${(data.current_month_sales || 0).toFixed(2)}`);
    updateElement('month-journeys', data.current_month_journeys || 0);
    updateElement('month-loyal-customer', data.loyal_customer || 'N/A');
    updateElement('month-top-station', data.top_station || 'N/A');

    // Update today's stats
    updateElement('today-signups', data.today_signups || 0);
    updateElement('today-tickets', data.today_tickets || 0);
    updateElement('today-sales', `£${(data.today_sales || 0).toFixed(2)}`);

    // Update yesterday's stats
    updateElement('yesterday-signups', data.yesterday_signups || 0);
    updateElement('yesterday-tickets', data.yesterday_tickets || 0);
    updateElement('yesterday-sales', `£${(data.yesterday_sales || 0).toFixed(2)}`);

    // Update differences
    const signupDiff = (data.today_signups || 0) - (data.yesterday_signups || 0);
    const ticketDiff = (data.today_tickets || 0) - (data.yesterday_tickets || 0);
    const salesDiff = (data.today_sales || 0) - (data.yesterday_sales || 0);

    updateElement('today-signups-diff', `${signupDiff >= 0 ? '+' : ''}${signupDiff} (vs Previous Day)`);
    updateElement('today-tickets-diff', `${ticketDiff >= 0 ? '+' : ''}${ticketDiff} (vs Previous Day)`);
    updateElement('today-sales-diff', `${salesDiff >= 0 ? '+' : ''}£${salesDiff.toFixed(2)} (vs Previous Day)`);
}

async function loadTodaySignups() {
    try {
        const signupsData = await apiCall('/api/analytics/today-signups');

        if (signupsData && signupsData.status === 'success') {
            updateTodaySignupsTable(signupsData.data.signups || []);
        }
    } catch (error) {
        console.error('Error loading today signups:', error);
        updateTodaySignupsTable([]);
    }
}

function updateTodaySignupsTable(signups) {
    const tableBody = document.querySelector('#today-signups-table tbody');

    if (!tableBody) return;

    if (signups.length === 0) {
        tableBody.innerHTML = '<tr><td colspan="3" class="text-center">No signups today</td></tr>';
        return;
    }

    const rows = signups.map(signup => `
        <tr>
            <td>${signup.email || 'N/A'}</td>
            <td>${formatDate(signup.signup_date)}</td>
            <td>${signup.same_day_ticket_purchase > 0 ?
                `<strong>${signup.same_day_ticket_purchase}</strong>` :
                '0'}</td>
        </tr>
    `).join('');

    tableBody.innerHTML = rows;

    // Setup CSV download
    setupCSVDownload(signups);
}

function setupCSVDownload(signups) {
    const downloadBtn = document.getElementById('download-signups-csv');
    if (!downloadBtn) return;

    downloadBtn.onclick = function() {
        const csv = convertToCSV(signups);
        const today = new Date().toISOString().split('T')[0];
        downloadCSV(csv, `today_signup_data_${today}.csv`);
    };
}

function convertToCSV(data) {
    if (!data || data.length === 0) return '';

    const headers = Object.keys(data[0]);
    const csvContent = [
        headers.join(','),
        ...data.map(row => headers.map(header => `"${row[header] || ''}"`).join(','))
    ].join('\n');

    return csvContent;
}

function downloadCSV(csv, filename) {
    const blob = new Blob([csv], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.setAttribute('hidden', '');
    a.setAttribute('href', url);
    a.setAttribute('download', filename);
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
}

function setCurrentDateAndMonth() {
    const now = new Date();
    const today = now.toLocaleDateString('en-GB', {
        month: 'long',
        day: 'numeric'
    });
    const currentMonth = now.toLocaleDateString('en-GB', {
        month: 'long',
        year: 'numeric'
    });

    updateElement('today-date', today);
    updateElement('current-month', currentMonth);
}

function updateElement(id, value) {
    const element = document.getElementById(id);
    if (element) {
        element.textContent = value;

        // Hide spinner and show text for stat cards
        const spinner = element.querySelector('.spinner-border');
        const textSpan = element.querySelector('span[id$="Text"]');

        if (spinner) spinner.style.display = 'none';
        if (textSpan) {
            textSpan.textContent = value;
            textSpan.style.display = 'inline';
        }
    }
}

function formatDate(dateString) {
    if (!dateString) return 'N/A';

    try {
        const date = new Date(dateString);
        return date.toLocaleDateString('en-GB', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    } catch (error) {
        return dateString;
    }
}

function refreshDashboard() {
    // Show loading state
    const refreshBtn = document.querySelector('button[onclick="refreshDashboard()"]');
    if (refreshBtn) {
        const originalText = refreshBtn.innerHTML;
        refreshBtn.innerHTML = '<i class="bi bi-arrow-clockwise"></i> Refreshing...';
        refreshBtn.disabled = true;

        // Reload dashboard data
        loadDashboardData().finally(() => {
            refreshBtn.innerHTML = originalText;
            refreshBtn.disabled = false;
        });
    } else {
        loadDashboardData();
    }
}

function showErrorMessage(message) {
    // You can implement a toast notification or alert here
    console.error(message);
}

// Load dashboard data when on dashboard page
if (window.location.pathname === '/dashboard/' || window.location.pathname === '/dashboard') {
    document.addEventListener('DOMContentLoaded', function() {
        setTimeout(loadDashboardData, 500); // Small delay to ensure DOM is ready
    });
}
