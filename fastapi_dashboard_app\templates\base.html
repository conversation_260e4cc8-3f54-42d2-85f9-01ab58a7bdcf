<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Dashboard{% endblock %}</title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
    
    <!-- Plotly.js -->
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    
    <!-- Custom CSS -->
    <link href="{{ url_for('static', path='/css/dashboard.css') }}" rel="stylesheet">
    
    {% block extra_head %}{% endblock %}
</head>
<body>
    {% if user %}
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand" href="/dashboard/">
                <!-- <i class="bi bi-speedometer2"></i> -->
                JurnyOn Dashboard
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/dashboard/">
                            <i class="bi bi-house"></i> Overview
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/dashboard/users">
                            <i class="bi bi-people"></i> Users
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/dashboard/tickets">
                            <i class="bi bi-ticket"></i> Tickets
                        </a>
                    </li>
                    <!-- <li class="nav-item">
                        <a class="nav-link" href="/dashboard/maps">
                            <i class="bi bi-geo-alt"></i> Maps
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/dashboard/notifications">
                            <i class="bi bi-bell"></i> Notifications
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/dashboard/reports">
                            <i class="bi bi-file-earmark-text"></i> Reports
                        </a>
                    </li> -->
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-person-circle"></i>
                            {{ user.username }}
                            <span class="badge bg-secondary">{{ user.user_role }}</span>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" onclick="logout()">
                                <i class="bi bi-box-arrow-right"></i> Logout
                            </a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>
    
    <!-- Main Content -->
    <main class="main-content">
        <div class="container-fluid">
            {% block content %}{% endblock %}
        </div>
    </main>
    
    {% else %}
    <!-- Login Content -->
    <main class="login-main">
        {% block login_content %}{% endblock %}
    </main>
    {% endif %}
    
    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Axios for API calls -->
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    
    <!-- Custom JavaScript -->
    <script src="{{ url_for('static', path='/js/dashboard.js') }}"></script>

    <!-- Authentication Check for Dashboard Pages -->
    {% if user and request.url.path.startswith('/dashboard/') %}
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // Check if user is authenticated when on dashboard pages
        const token = localStorage.getItem('access_token');
        if (!token) {
            // No token found, redirect to login
            window.location.href = '/auth/login';
            return;
        }

        // Update user info in navbar if we have real token data
        const username = localStorage.getItem('username');
        const userRole = localStorage.getItem('user_role');

        if (username && userRole) {
            // Update the navbar with real user info
            const userDropdown = document.querySelector('.navbar-nav .dropdown-toggle');
            if (userDropdown) {
                userDropdown.innerHTML = `
                    <i class="bi bi-person-circle"></i>
                    ${username}
                    <span class="badge bg-secondary">${userRole}</span>
                `;
            }
        }
    });
    </script>
    {% endif %}

    {% block extra_scripts %}{% endblock %}
</body>
</html>
