"""
Analytics service for handling external API calls
Converted from the original Streamlit application
"""

import httpx
from typing import Optional, Tuple, List, Dict, Any
from app.core.config import settings


class AuthenticationExpiredException(Exception):
    """Exception raised when authentication token has expired"""
    pass

class AnalyticsService:
    def __init__(self, token: str):
        self.token = token
        # Use the token exactly as the original Streamlit app does
        self.headers = {"Authorization": f"Bearer {token}"} if token else {}
    
    async def get_today_signups(self) -> Tuple[Optional[int], Optional[List[Dict]]]:
        """Get today's signups from API"""
        try:
            if not self.token:
                raise Exception("No authentication token provided")

            async with httpx.AsyncClient() as client:
                response = await client.get(
                    f"{settings.BASE_API_URL}/getTodaySignups",
                    headers=self.headers,
                    timeout=10.0
                )

            if response.status_code == 200:
                json_response = response.json()
                count = json_response.get("SignupCount")
                data = json_response.get("Signups")
                return count, data
            elif response.status_code == 403:
                raise AuthenticationExpiredException("Authentication expired")
            else:
                print(f"Error in API response: {response.status_code}")
                return None, None

        except Exception as e:
            print(f"Error in get_today_signups: {e}")
            return None, None


    
    async def get_today_tickets(self) -> Tuple[Optional[int], Optional[List[Dict]]]:
        """Get today's ticket purchases from API"""
        try:
            if not self.token:
                raise Exception("No authentication token provided")

            async with httpx.AsyncClient() as client:
                response = await client.get(
                    f"{settings.BASE_API_URL}/getTodayTickets",
                    headers=self.headers,
                    timeout=10.0
                )

            if response.status_code == 200:
                json_response = response.json()
                count = json_response.get("TicketCount")
                data = json_response.get("Tickets")
                return count, data
            elif response.status_code == 403:
                raise AuthenticationExpiredException("Authentication expired")
            else:
                print(f"Error in API response: {response.status_code}")
                return None, None

        except Exception as e:
            print(f"Error in get_today_tickets: {e}")
            return None, None


    
    async def get_all_users(self) -> Tuple[Optional[int], Optional[List[Dict]]]:
        """Get all users from API"""
        try:
            if not self.token:
                raise Exception("No authentication token provided")

            async with httpx.AsyncClient() as client:
                response = await client.get(
                    f"{settings.BASE_API_URL}/getAllUser",
                    headers=self.headers,
                    timeout=10.0
                )

            if response.status_code == 200:
                json_response = response.json()
                count = json_response.get("TotalSignup")
                data = json_response.get("SignupResult")
                return count, data
            elif response.status_code == 403:
                raise AuthenticationExpiredException("Authentication expired")
            else:
                print(f"Error in API response: {response.status_code}")
                return None, None

        except Exception as e:
            print(f"Error in get_all_users: {e}")
            return None, None


    
    async def get_all_tickets(self) -> Tuple[Optional[int], Optional[List[Dict]]]:
        """Get all tickets from API"""
        try:
            if not self.token:
                raise Exception("No authentication token provided")

            async with httpx.AsyncClient() as client:
                response = await client.get(
                    f"{settings.BASE_API_URL}/getAllTickets",
                    headers=self.headers,
                    timeout=10.0
                )

            if response.status_code == 200:
                json_response = response.json()
                count = json_response.get("TotalTicket")
                data = json_response.get("TicketResult")
                return count, data
            elif response.status_code == 403:
                raise AuthenticationExpiredException("Authentication expired")
            else:
                print(f"Error in API response: {response.status_code}")
                return None, None

        except Exception as e:
            print(f"Error in get_all_tickets: {e}")
            return None, None


    
    async def get_signups_between_dates(self, start_date: str, end_date: str) -> Tuple[Optional[int], Optional[List[Dict]]]:
        """Get signups between dates"""
        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{settings.BASE_API_URL}/getAllUserBetweenDate",
                    json={"StartDate": start_date, "EndDate": end_date},
                    headers=self.headers
                )
            
            if response.status_code == 200:
                json_response = response.json()
                count = json_response.get("SignupTotal")
                data = json_response.get("SignupResult")
                return count, data
            elif response.status_code == 403:
                raise AuthenticationExpiredException("Authentication expired")
            else:
                print(f"Error in API response: {response.status_code}")
                return None, None
                
        except Exception as e:
            print(f"Error in get_signups_between_dates: {e}")
            return None, None
    
    async def get_tickets_between_dates(self, start_date: str, end_date: str) -> Tuple[Optional[int], Optional[List[Dict]]]:
        """Get tickets between dates"""
        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{settings.BASE_API_URL}/getAllTicketBetweenDate",
                    json={"StartDate": str(start_date), "EndDate": str(end_date)},
                    headers=self.headers
                )
            
            if response.status_code == 200:
                json_response = response.json()
                count = json_response.get("TicketCount")
                data = json_response.get("TicketResult")
                return count, data
            elif response.status_code == 403:
                raise AuthenticationExpiredException("Authentication expired")
            else:
                print(f"Error in API response: {response.status_code}")
                return None, None
                
        except Exception as e:
            print(f"Error in get_tickets_between_dates: {e}")
            return None, None
    
    async def get_user_tickets(self, email: str) -> Tuple[Optional[int], Optional[List[Dict]], Optional[int], Optional[Dict]]:
        """Get all tickets for a specific user"""
        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{settings.BASE_API_URL}/getCustomerAllTickets",
                    json={"Email": email},
                    headers=self.headers
                )
            
            if response.status_code == 200:
                json_response = response.json()
                count = json_response.get("Total User Ticket", 0)
                data = json_response.get("User Ticket")
                loyalty_points = json_response.get("TotalLoyaltyPoints", 0)
                
                # Extract subscription data
                user_info = json_response.get("UserInfo", {})
                subscription_data = {
                    "ActivePlan": user_info.get("ActivePlan", "N/A"),
                    "CurrentSubscription": user_info.get("SubscriptionPlan", "N/A"),
                    "SubscriptionExpire": user_info.get("SubscriptionExpire", "N/A")
                }
                
                return count, data, loyalty_points, subscription_data
            elif response.status_code == 403:
                raise AuthenticationExpiredException("Authentication expired")
            else:
                print(f"Error in API response: {response.status_code}")
                return None, None, None, None
                
        except Exception as e:
            print(f"Error in get_user_tickets: {e}")
            return None, None, None, None
    
    async def get_all_referral_info(self) -> Optional[List[Dict]]:
        """Get all referral information"""
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    f"{settings.BASE_API_URL}/loadAllReferralInformation",
                    headers=self.headers
                )
            
            if response.status_code == 200:
                json_response = response.json()
                if json_response.get("Status") == "success":
                    return json_response.get("ReferralResult")
                else:
                    print(f"Failed to fetch referral info: {json_response.get('Message')}")
                    return None
            elif response.status_code == 403:
                raise AuthenticationExpiredException("Authentication expired")
            else:
                print(f"Error in API response: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"Error in get_all_referral_info: {e}")
            return None
