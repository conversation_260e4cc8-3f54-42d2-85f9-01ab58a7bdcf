"""
Analytics service for handling external API calls
Converted from the original Streamlit application
"""

import httpx
from typing import Optional, Tuple, List, Dict, Any
from app.core.config import settings
import pandas as pd
from app.utils.helper import *


class AuthenticationExpiredException(Exception):
    """Exception raised when authentication token has expired"""
    pass

class AnalyticsService:
    def __init__(self, token: str):
        self.token = token
        # Use the token exactly as the original Streamlit app does
        self.headers = {"Authorization": f"Bearer {token}"} if token else {}
    
    async def get_today_signups(self) -> Tuple[Optional[int], Optional[List[Dict]]]:
        """Get today's signups from API"""
        try:
            if not self.token:
                raise Exception("No authentication token provided")

            async with httpx.AsyncClient() as client:
                response = await client.get(
                    f"{settings.BASE_API_URL}/getTodaySignups",
                    headers=self.headers,
                    timeout=10.0
                )

            if response.status_code == 200:
                json_response = response.json()
                count = json_response.get("SignupCount")
                data = json_response.get("Signups")
                return count, data
            elif response.status_code == 403:
                raise AuthenticationExpiredException("Authentication expired")
            else:
                print(f"Error in API response: {response.status_code}")
                return None, None

        except Exception as e:
            print(f"Error in get_today_signups: {e}")
            return None, None


    
    async def get_today_tickets(self) -> Tuple[Optional[int], Optional[List[Dict]]]:
        """Get today's ticket purchases from API"""
        try:
            if not self.token:
                raise Exception("No authentication token provided")

            async with httpx.AsyncClient() as client:
                response = await client.get(
                    f"{settings.BASE_API_URL}/getTodayTickets",
                    headers=self.headers,
                    timeout=10.0
                )

            if response.status_code == 200:
                json_response = response.json()
                count = json_response.get("TicketCount")
                data = json_response.get("Tickets")
                return count, data
            elif response.status_code == 403:
                raise AuthenticationExpiredException("Authentication expired")
            else:
                print(f"Error in API response: {response.status_code}")
                return None, None

        except Exception as e:
            print(f"Error in get_today_tickets: {e}")
            return None, None


    
    async def get_all_users(self) -> Tuple[Optional[int], Optional[List[Dict]]]:
        """Get all users from API"""
        try:
            if not self.token:
                raise Exception("No authentication token provided")

            async with httpx.AsyncClient() as client:
                response = await client.get(
                    f"{settings.BASE_API_URL}/getAllUser",
                    headers=self.headers,
                    timeout=10.0
                )

            if response.status_code == 200:
                json_response = response.json()
                count = json_response.get("TotalSignup")
                data = json_response.get("SignupResult")
                return count, data
            elif response.status_code == 403:
                raise AuthenticationExpiredException("Authentication expired")
            else:
                print(f"Error in API response: {response.status_code}")
                return None, None

        except Exception as e:
            print(f"Error in get_all_users: {e}")
            return None, None

    async def get_all_deleted_users(self) -> Tuple[Optional[int], Optional[List[Dict]]]:
        """Get all deleted users from API"""
        try:
            if not self.token:
                raise Exception("No authentication token provided")

            async with httpx.AsyncClient() as client:
                response = await client.get(
                    f"{settings.BASE_API_URL}/getAllDeletedRequestedUser",
                    headers=self.headers,
                    timeout=10.0
                )

            if response.status_code == 200:
                json_response = response.json()
                count = json_response.get("TotalDeleted")
                data = json_response.get("DeletedResult")
                final_data = []

                if data:
                    for data in data:
                        row = {
                            "Id": data.get("_id"),
                            "UserId": data.get("UserId"),    
                            "DeleteReason": data.get("DeleteReason"),
                            "Status": data.get("Status"),
                            "DateTime": data.get('DateTime'),
                            "FirstName": data.get('UserInfo').get("FirstName"),
                            "LastName": data.get('UserInfo').get("LastName"),
                            "Email": data.get('UserInfo').get("Email")
                        }

                        final_data.append(row)

                return count, final_data
            elif response.status_code == 403:
                raise AuthenticationExpiredException("Authentication expired")
            else:
                print(f"Error in API response: {response.status_code}")
                return None, None

        except Exception as e:
            print(f"Error in get_all_deleted_users: {e}")
            return None, None
    
    async def get_all_tickets(self) -> Tuple[Optional[int], Optional[List[Dict]]]:
        """Get all tickets from API"""
        try:
            if not self.token:
                raise Exception("No authentication token provided")

            async with httpx.AsyncClient() as client:
                response = await client.get(
                    f"{settings.BASE_API_URL}/getAllTickets",
                    headers=self.headers,
                    timeout=10.0
                )

            if response.status_code == 200:
                json_response = response.json()
                count = json_response.get("TotalTicket")
                data = json_response.get("TicketResult")

                final_data = []

                if data:
                    for data in data:
                        extracted_ticket_info = parse_ticket_info(str(data.get("JourneyDate")))
                        JourneyType = extracted_ticket_info.get('journeyType')

                        ticket_info = {
                            "FirstName": data.get("UserInfo").get("FirstName"),
                            "LastName": data.get("UserInfo").get("LastName"),
                            "Email": data.get("UserInfo").get("Email"),
                            "LoyaltyPoints": data.get("UserInfo").get("LoyaltyPoints"),
                            "ActivePlan": data.get("UserInfo").get("ActivePlan"),
                            "PaymentTime": data.get('PaymentTime'),
                            "Amount": data.get('Amount'),
                            "Fromlocation": data.get('Fromlocation'),
                            "Arrivallocation": data.get('Arrivallocation'),
                            "RecordLocator": data.get('RecordLocator'),
                            "JourneyDate": data.get('JourneyDate'),
                            "JourneyType": JourneyType
                        }


                        final_data.append(ticket_info)





                return count, final_data
            elif response.status_code == 403:
                raise AuthenticationExpiredException("Authentication expired")
            else:
                print(f"Error in API response: {response.status_code}")
                return None, None

        except Exception as e:
            print(f"Error in get_all_tickets: {e}")
            return None, None


    
    async def get_signups_between_dates(self, start_date: str, end_date: str) -> Tuple[Optional[int], Optional[List[Dict]]]:
        """Get signups between dates"""
        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{settings.BASE_API_URL}/getAllUserBetweenDate",
                    json={"StartDate": start_date, "EndDate": end_date},
                    headers=self.headers
                )
            
            if response.status_code == 200:
                json_response = response.json()
                count = json_response.get("SignupTotal")
                data = json_response.get("SignupResult")
                return count, data
            elif response.status_code == 403:
                raise AuthenticationExpiredException("Authentication expired")
            else:
                print(f"Error in API response: {response.status_code}")
                return None, None
                
        except Exception as e:
            print(f"Error in get_signups_between_dates: {e}")
            return None, None
    
    async def get_tickets_between_dates(self, start_date: str, end_date: str) -> Tuple[Optional[int], Optional[List[Dict]]]:
        """Get tickets between dates"""
        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{settings.BASE_API_URL}/getAllTicketBetweenDate",
                    json={"StartDate": str(start_date), "EndDate": str(end_date)},
                    headers=self.headers
                )
            
            if response.status_code == 200:
                json_response = response.json()
                count = json_response.get("TicketCount")
                data = json_response.get("TicketResult")
                return count, data
            elif response.status_code == 403:
                raise AuthenticationExpiredException("Authentication expired")
            else:
                print(f"Error in API response: {response.status_code}")
                return None, None
                
        except Exception as e:
            print(f"Error in get_tickets_between_dates: {e}")
            return None, None
    
    async def get_user_tickets(self, email: str) -> Tuple[Optional[int], Optional[List[Dict]], Optional[int], Optional[Dict]]:
        """Get all tickets for a specific user"""
        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{settings.BASE_API_URL}/getCustomerAllTickets",
                    json={"Email": email},
                    headers=self.headers
                )
            
            if response.status_code == 200:
                json_response = response.json()
                count = json_response.get("Total User Ticket", 0)
                data = json_response.get("User Ticket")
                loyalty_points = json_response.get("TotalLoyaltyPoints", 0)
                
                # Extract subscription data
                user_info = json_response.get("UserInfo", {})
                subscription_data = {
                    "ActivePlan": user_info.get("ActivePlan", "N/A"),
                    "CurrentSubscription": user_info.get("SubscriptionPlan", "N/A"),
                    "SubscriptionExpire": user_info.get("SubscriptionExpire", "N/A")
                }
                
                return count, data, loyalty_points, subscription_data
            elif response.status_code == 403:
                raise AuthenticationExpiredException("Authentication expired")
            else:
                print(f"Error in API response: {response.status_code}")
                return None, None, None, None
                
        except Exception as e:
            print(f"Error in get_user_tickets: {e}")
            return None, None, None, None
    
    async def get_all_referral_info(self) -> Optional[List[Dict]]:
        """Get all referral information"""
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    f"{settings.BASE_API_URL}/loadAllReferralInformation",
                    headers=self.headers
                )
            
            if response.status_code == 200:
                json_response = response.json()
                if json_response.get("Status") == "success":
                    return json_response.get("ReferralResult")
                else:
                    print(f"Failed to fetch referral info: {json_response.get('Message')}")
                    return None
            elif response.status_code == 403:
                raise AuthenticationExpiredException("Authentication expired")
            else:
                print(f"Error in API response: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"Error in get_all_referral_info: {e}")
            return None


    async def get_zero_ticket_buyers_df(self, all_user_df: pd.DataFrame, 
                                        all_ticket_df: pd.DataFrame, 
                                        deleted_df: pd.DataFrame) -> Tuple[int, pd.DataFrame]:
        try:
            # Ensure Email case consistency
            all_user_df["Email"] = all_user_df["Email"].str.lower()
            all_ticket_df["Email"] = all_ticket_df["Email"].str.lower()

            deleted_email_list = []
            if deleted_df is not None and not deleted_df.empty:
                deleted_df["Email"] = deleted_df["Email"].str.lower()
                deleted_email_list = deleted_df["Email"].tolist()

            # Remove deleted users to get active users
            active_user_df = all_user_df[~all_user_df["Email"].isin(deleted_email_list)].copy()
            active_ticket_df = all_ticket_df[~all_ticket_df["Email"].isin(deleted_email_list)].copy()       

            # Get unique ticket buyers from active users
            ticket_buyer_emails = set(active_ticket_df["Email"].dropna())
            

            # Zero ticket buyers = active users who are not in ticket buyer list
            zero_ticket_buyers = active_user_df[~active_user_df["Email"].isin(ticket_buyer_emails)]
            zero_ticket_buyers = zero_ticket_buyers.drop_duplicates(subset="Email")
            

            return zero_ticket_buyers.shape[0], zero_ticket_buyers

        except Exception as e:
            print('❌ Error in get_zero_ticket_buyers:', e)
            return 0, pd.DataFrame()
#

    async def get_ticket_buyers_df(self,all_user_df: pd.DataFrame, 
                                        all_ticket_df: pd.DataFrame, 
                                        deleted_df: pd.DataFrame,
                                        include_ticket_count=False
                                    )-> Tuple[int, pd.DataFrame]:
        try:

            # Normalize email casing
            all_user_df["Email"] = all_user_df["Email"].str.lower()
            all_ticket_df["Email"] = all_ticket_df["Email"].str.lower()
            
            
            if include_ticket_count:
                ticket_counts = all_ticket_df.groupby('Email').size().reset_index(name='TicketCount')        
                all_user_df = pd.merge(all_user_df, ticket_counts, on='Email', how='inner')

            deleted_email_list = []
            if deleted_df is not None and not deleted_df.empty:
                deleted_df["Email"] = deleted_df["Email"].str.lower()
                deleted_email_list = deleted_df["Email"].tolist()

            active_user_df = all_user_df[~all_user_df["Email"].isin(deleted_email_list)].copy()
            active_ticket_df = all_ticket_df[~all_ticket_df["Email"].isin(deleted_email_list)].copy()

            # Match users who bought tickets
            ticket_buyer_emails = set(active_ticket_df["Email"].dropna())
            matched_users = active_user_df[active_user_df["Email"].isin(ticket_buyer_emails)]
            matched_users = matched_users.drop_duplicates(subset="Email")

            return matched_users.shape[0], matched_users

        except Exception as e:
            print('❌ Error in get_ticket_buyers:', e)
            return 0, pd.DataFrame()
        
    async def get_total_journey_count(self, ticket_df:pd.DataFrame, create_new_col= False, create_from_col_name='') -> int:

        if not ticket_df.empty:
            try: 

                JourneyCount = ticket_df.shape[0]          

                if "JourneyType" in ticket_df.columns:                
                    return_ticket_df = ticket_df[ticket_df['JourneyType'] == 'Return']
                    JourneyCount = JourneyCount + return_ticket_df.shape[0]

                if create_new_col:
                    ticket_df['JourneyType'] = ticket_df[create_from_col_name].apply(lambda x: "Return" if pd.notna(x) and str(x).strip() != "" else "Single")
                    return_ticket_df = ticket_df[ticket_df['JourneyType'] == 'Return']
                    JourneyCount = JourneyCount + return_ticket_df.shape[0]

                return JourneyCount            
            
            except Exception as e:
                print("Error in get_total_journey_count:", e)
                return 0
        else:
            return 0

    async def get_dashboard_summary(self):
        """Get comprehensive dashboard summary data"""
        try:
            # Get all the individual stats
            today_signups_count, _ = await self.get_today_signups()
            today_tickets_count, today_tickets_data = await self.get_today_tickets()
            total_users_count, _ = await self.get_all_users()
            total_tickets_count, _ = await self.get_all_tickets()

            # Calculate additional stats
            summary_data = {
                "today_signups": today_signups_count or 0,
                "today_tickets": today_tickets_count or 0,
                "total_users": total_users_count or 0,
                "total_tickets": total_tickets_count or 0,

                # Placeholder values - these would need to be calculated from your data
                "zero_ticket_buyers": 0,
                "ticket_buyers": 0,
                "deleted_users": 0,
                "total_journeys": 0,

                # Current month stats
                "current_month_tickets": 0,
                "current_month_signups": 0,
                "current_month_new_buyers": 0,
                "current_month_sales": 0.0,
                "current_month_journeys": 0,
                "loyal_customer": "N/A",
                "top_station": "N/A",

                # Yesterday's stats
                "yesterday_signups": 0,
                "yesterday_tickets": 0,
                "yesterday_sales": 0.0,

                # Today's sales
                "today_sales": 0.0
            }

            # Calculate today's sales if we have ticket data
            if today_tickets_data:
                try:
                    total_sales = sum(float(ticket.get('Amount', 0)) for ticket in today_tickets_data if ticket.get('Amount'))
                    summary_data["today_sales"] = total_sales
                except (ValueError, TypeError):
                    summary_data["today_sales"] = 0.0

            return summary_data

        except Exception as e:
            print(f"Error getting dashboard summary: {e}")
            # Return default values on error
            return {
                "today_signups": 0,
                "today_tickets": 0,
                "total_users": 0,
                "total_tickets": 0,
                "zero_ticket_buyers": 0,
                "ticket_buyers": 0,
                "deleted_users": 0,
                "total_journeys": 0,
                "current_month_tickets": 0,
                "current_month_signups": 0,
                "current_month_new_buyers": 0,
                "current_month_sales": 0.0,
                "current_month_journeys": 0,
                "loyal_customer": "N/A",
                "top_station": "N/A",
                "yesterday_signups": 0,
                "yesterday_tickets": 0,
                "yesterday_sales": 0.0,
                "today_sales": 0.0
            }