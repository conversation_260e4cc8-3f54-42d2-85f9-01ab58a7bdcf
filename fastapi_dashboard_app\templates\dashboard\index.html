{% extends "base.html" %}

{% block title %}{{ page_title }} - Dashboard{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col text-center">
        <h1 class="h3 mb-0">
            <i class="text-primary"></i>
            Overview
        </h1>
        <p class="text-muted">Overall Summary Stats</p>
    </div>
</div>

<!-- Stats Cards -->
<div class="row mb-4">
    <div class="col-xl-2 col-md-2 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="font-weight-bold text-uppercase mb-1 card-title">
                            Total User
                        </div>
                        <div class="h5 mb-0 font-weight-bold card-value" id="totalUserCount">
                            <!-- Spinner for loading -->
                            <div class="spinner-border spinner-border-sm" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <!-- Actual count (hidden initially) -->
                            <span id="totalUserCountText" style="display:none;">{{ total_user_count }}</span>
                        </div>
                    </div>
                    <div class="col-auto">
                        <!-- <i class="bi bi-people-fill fa-2x text-primary"></i> -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-2 col-md-2 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="font-weight-bold card-title text-uppercase mb-1">
                            Zero Ticket Buyer
                        </div>
                        <div class="h5 mb-0 font-weight-bold card-value" id="zeroTicketBuyerCount">
                            <!-- Spinner for loading -->
                            <div class="spinner-border spinner-border-sm" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <!-- Actual count (hidden initially) -->
                            <span id="zeroTicketBuyerCountText" style="display:none;">{{ zero_ticket_buyer_count }}</span>
                        </div>
                    </div>
                    <div class="col-auto">
                        <!-- <i class="bi bi-ticket fa-2x text-primary"></i> -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-2 col-md-2 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="font-weight-bold card-title text-uppercase mb-1">
                            Ticket Buyer
                        </div>
                        <div class="h5 mb-0 font-weight-bold card-value" id="ticketBuyerCount">
                            <!-- Spinner for loading -->
                            <div class="spinner-border spinner-border-sm" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <!-- Actual count (hidden initially) -->
                            <span id="ticketBuyerCountText" style="display:none;">{{ ticket_buyer_count }}</span>
                        </div>
                    </div>
                    <div class="col-auto">
                        <!-- <i class="bi bi-ticket fa-2x text-primary"></i> -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-2 col-md-2 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="font-weight-bold card-title text-uppercase mb-1">
                            Deleted User
                        </div>
                        <div class="h5 mb-0 font-weight-bold card-value" id="deletedUserCount">
                            <!-- Spinner for loading -->
                            <div class="spinner-border spinner-border-sm" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <!-- Actual count (hidden initially) -->
                            <span id="deletedUserCountText" style="display:none;">{{ deleted_user_count }}</span>
                        </div>
                    </div>
                    <div class="col-auto">
                        <!-- <i class="bi bi-ticket fa-2x text-primary"></i> -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-2 col-md-2 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="font-weight-bold card-title text-uppercase mb-1">
                            Total Journey
                        </div>
                        <div class="h5 mb-0 font-weight-bold card-value" id="totalJourneyCount">
                            <!-- Spinner for loading -->
                            <div class="spinner-border spinner-border-sm" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <!-- Actual count (hidden initially) -->
                            <span id="totalJourneyCountText" style="display:none;">{{ total_journey_count }}</span>
                        </div>
                    </div>
                    <div class="col-auto">
                        <!-- <i class="bi bi-ticket fa-2x text-primary"></i> -->
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Current Month Stats Section -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Current Month's Stats (<span id="current-month">Loading...</span>)</h6>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-md-4">
                        <div class="metric-card">
                            <div class="metric-label">Tickets Sold This Month</div>
                            <div class="metric-value" id="month-tickets">Loading...</div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="metric-card">
                            <div class="metric-label">Signups This Month</div>
                            <div class="metric-value" id="month-signups">Loading...</div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="metric-card">
                            <div class="metric-label">New Ticket Buyers</div>
                            <div class="metric-value" id="month-new-buyers">Loading...</div>
                        </div>
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-6">
                        <div class="metric-card">
                            <div class="metric-label">Total Sale This Month</div>
                            <div class="metric-value" id="month-sales">Loading...</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="metric-card">
                            <div class="metric-label">Total Journey This Month</div>
                            <div class="metric-value" id="month-journeys">Loading...</div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="metric-card">
                            <div class="metric-label">Loyal Customer This Month</div>
                            <div class="metric-value" id="month-loyal-customer">Loading...</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="metric-card">
                            <div class="metric-label">Station of The Month</div>
                            <div class="metric-value" id="month-top-station">Loading...</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Today's Stats -->
<div class="row mb-4">
    <div class="col-12">
        <h4 class="mb-3">Today's Stats (<span id="today-date">Loading...</span>)</h4>
    </div>
</div>
<div class="row mb-4">
    <div class="col-xl-4 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            Today's Signups
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="today-signups">
                            Loading...
                        </div>
                        <div class="text-xs text-muted" id="today-signups-diff">vs Previous Day</div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-person-plus fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-4 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            Today's Ticket Purchases
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="today-tickets">
                            Loading...
                        </div>
                        <div class="text-xs text-muted" id="today-tickets-diff">vs Previous Day</div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-ticket fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-4 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            Today's Sale
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="today-sales">
                            Loading...
                        </div>
                        <div class="text-xs text-muted" id="today-sales-diff">vs Previous Day</div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-currency-pound fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Yesterday's Stats -->
<div class="row mb-4">
    <div class="col-12">
        <h4 class="mb-3">Yesterday's Stats</h4>
    </div>
</div>
<div class="row mb-4">
    <div class="col-xl-4 col-md-6 mb-4">
        <div class="card border-left-secondary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-secondary text-uppercase mb-1">
                            Yesterday's Signups
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="yesterday-signups">
                            Loading...
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-person-plus fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-4 col-md-6 mb-4">
        <div class="card border-left-secondary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-secondary text-uppercase mb-1">
                            Yesterday's Ticket Purchases
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="yesterday-tickets">
                            Loading...
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-ticket fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-4 col-md-6 mb-4">
        <div class="card border-left-secondary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-secondary text-uppercase mb-1">
                            Yesterday's Sale
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="yesterday-sales">
                            Loading...
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-currency-pound fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Today's Signups Table -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Today's Signups</h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered" id="today-signups-table">
                        <thead>
                            <tr>
                                <th>Email</th>
                                <th>Signup Date</th>
                                <th>Same Day Ticket Purchase</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td colspan="3" class="text-center">Loading...</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div class="mt-3">
                    <button class="btn btn-success" id="download-signups-csv">
                        <i class="bi bi-download"></i> Download as CSV
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Charts Row -->
<!-- <div class="row mb-4">
    <div class="col-xl-8 col-lg-7">
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="bi bi-graph-up"></i>
                    Signups & Tickets Overview
                </h6>
                <div class="dropdown no-arrow">
                    <a class="dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="bi bi-three-dots-vertical text-gray-400"></i>
                    </a>
                    <div class="dropdown-menu dropdown-menu-right shadow">
                        <a class="dropdown-item" href="#" onclick="refreshCharts()">
                            <i class="bi bi-arrow-clockwise"></i> Refresh
                        </a>
                        <a class="dropdown-item" href="/dashboard/analytics">
                            <i class="bi bi-graph-up"></i> View Analytics
                        </a>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div id="overviewChart" style="height: 400px;"></div>
            </div>
        </div>
    </div>

    <div class="col-xl-4 col-lg-5">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="bi bi-pie-chart"></i>
                    User Distribution
                </h6>
            </div>
            <div class="card-body">
                <div id="userDistributionChart" style="height: 400px;"></div>
            </div>
        </div>
    </div>
</div> -->

<!-- Recent Activity -->
<!-- <div class="row">
    <div class="col-lg-6 mb-4">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="bi bi-clock-history"></i>
                    Recent Signups
                </h6>
            </div>
            <div class="card-body">
                <div id="recentSignups">
                    <div class="text-center">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-6 mb-4">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="bi bi-ticket"></i>
                    Recent Tickets
                </h6>
            </div>
            <div class="card-body">
                <div id="recentTickets">
                    <div class="text-center">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div> -->


{% endblock %}

{% block extra_scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    loadDashboardData();
    
    // Refresh data every 5 minutes
    setInterval(loadDashboardData, 5 * 60 * 1000);
});

async function loadDashboardData() {
    try {
        // Load summary data
        const summaryResponse = await apiCall('/api/dashboard-summary');
        if (summaryResponse.status === 'success') {
            updateSummaryCards(summaryResponse.data);
        }
        
        // // Load recent signups
        // const signupsResponse = await apiCall('/api/today-signups');
        // if (signupsResponse.status === 'success') {
        //     updateRecentSignups(signupsResponse.data.signups);
        // }
        
        // // Load recent tickets
        // const ticketsResponse = await apiCall('/api/today-tickets');
        // if (ticketsResponse.status === 'success') {
        //     updateRecentTickets(ticketsResponse.data.tickets);
        // }
        
        // // Create charts
        // createOverviewChart();
        // createUserDistributionChart();
        
    } catch (error) {
        console.error('Error loading dashboard data:', error);
        showAlert('Error loading dashboard data', 'danger');
    }
}

function updateSummaryCards(data) {
    document.getElementById('totalUserCount').textContent = data.total_user_count.toLocaleString();
    document.getElementById('zeroTicketBuyerCount').textContent = data.zero_ticket_buyer_count.toLocaleString();
    document.getElementById('deletedUserCount').textContent = data.deleted_user_count.toLocaleString();
    document.getElementById('ticketBuyerCount').textContent = data.ticket_buyer_count.toLocaleString();
    document.getElementById('totalJourneyCount').textContent = data.total_journey_count.toLocaleString();

}

function updateRecentSignups(signups) {
    const container = document.getElementById('recentSignups');
    if (!signups || signups.length === 0) {
        container.innerHTML = '<p class="text-muted">No recent signups</p>';
        return;
    }
    
    const html = signups.slice(0, 5).map(signup => `
        <div class="d-flex align-items-center mb-3">
            <div class="avatar-sm bg-primary rounded-circle d-flex align-items-center justify-content-center me-3">
                <i class="bi bi-person text-white"></i>
            </div>
            <div>
                <h6 class="mb-0">${signup.FirstName || ''} ${signup.LastName || ''}</h6>
                <small class="text-muted">${signup.Email || ''}</small>
            </div>
        </div>
    `).join('');
    
    container.innerHTML = html;
}

function updateRecentTickets(tickets) {
    const container = document.getElementById('recentTickets');
    if (!tickets || tickets.length === 0) {
        container.innerHTML = '<p class="text-muted">No recent tickets</p>';
        return;
    }
    
    const html = tickets.slice(0, 5).map(ticket => `
        <div class="d-flex align-items-center justify-content-between mb-3">
            <div>
                <h6 class="mb-0">${ticket.Fromlocation || ''} → ${ticket.Arrivallocation || ''}</h6>
                <small class="text-muted">${ticket.FirstName || ''} ${ticket.LastName || ''}</small>
            </div>
            <span class="badge bg-success">£${ticket.Amount || 0}</span>
        </div>
    `).join('');
    
    container.innerHTML = html;
}

function createOverviewChart() {
    // Sample data - replace with real data
    const trace1 = {
        x: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
        y: [12, 19, 3, 5, 2, 3, 9],
        type: 'scatter',
        mode: 'lines+markers',
        name: 'Signups',
        line: {color: '#007bff'}
    };
    
    const trace2 = {
        x: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
        y: [8, 15, 7, 12, 8, 10, 14],
        type: 'scatter',
        mode: 'lines+markers',
        name: 'Tickets',
        line: {color: '#28a745'}
    };
    
    const layout = {
        title: '',
        xaxis: {title: 'Day'},
        yaxis: {title: 'Count'},
        margin: {t: 20}
    };
    
    Plotly.newPlot('overviewChart', [trace1, trace2], layout, {responsive: true});
}

function createUserDistributionChart() {
    // Sample data - replace with real data
    const data = [{
        values: [70, 20, 10],
        labels: ['Active', 'Inactive', 'Pending'],
        type: 'pie',
        marker: {
            colors: ['#007bff', '#6c757d', '#ffc107']
        }
    }];
    
    const layout = {
        title: '',
        margin: {t: 20}
    };
    
    Plotly.newPlot('userDistributionChart', data, layout, {responsive: true});
}

function refreshCharts() {
    loadDashboardData();
}




</script>
{% endblock %}
